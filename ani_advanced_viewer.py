#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANI Advanced Viewer - <PERSON><PERSON> nâng cao để phân tích và export file animation .ani
Hỗ trợ export ra JSON, CSV và tạo visualization
"""

import struct
import json
import csv
import sys
import os
from datetime import datetime

class ANIAdvancedViewer:
    def __init__(self, filename):
        self.filename = filename
        self.data = None
        self.bones = []
        self.keyframes = []
        self.animation_info = {}
        
    def load_file(self):
        """Đọc file .ani"""
        try:
            with open(self.filename, 'rb') as f:
                self.data = f.read()
            print(f"✓ Đã đọc file {self.filename} ({len(self.data)} bytes)")
            return True
        except Exception as e:
            print(f"✗ Lỗi đọc file: {e}")
            return False
    
    def parse_detailed_structure(self):
        """Phân tích cấu trúc chi tiết của file"""
        print("\n=== PHÂN TÍCH CẤU TRÚC CHI TIẾT ===")
        
        # Phân tích header
        self.animation_info = {
            'file_size': len(self.data),
            'header_size': 0x200,
            'signature': self.data[0x200:0x206].decode('ascii', errors='ignore').rstrip('\x00'),
            'bone_count': 0,
            'keyframe_count': 0,
            'animation_length': 0
        }
        
        # Tìm bones và phân tích keyframes
        self._find_bones_and_keyframes()
        
        print(f"File size: {self.animation_info['file_size']} bytes")
        print(f"Signature: {self.animation_info['signature']}")
        print(f"Bones: {self.animation_info['bone_count']}")
        print(f"Estimated keyframes: {self.animation_info['keyframe_count']}")
    
    def _find_bones_and_keyframes(self):
        """Tìm bones và phân tích keyframes"""
        pos = 0
        bone_index = 0
        
        while pos < len(self.data) - 32:
            if self.data[pos:pos+5] == b'Bip01':
                # Đọc tên bone
                bone_name = ""
                name_start = pos
                for i in range(64):  # Tối đa 64 chars
                    if pos + i >= len(self.data):
                        break
                    char = self.data[pos + i]
                    if char == 0:
                        break
                    if 32 <= char <= 126:
                        bone_name += chr(char)
                    else:
                        break
                
                if len(bone_name) > 4:
                    # Tìm dữ liệu animation sau tên bone
                    data_start = name_start + len(bone_name) + 1
                    
                    # Align to 16-byte boundary
                    while data_start % 16 != 0:
                        data_start += 1
                    
                    # Phân tích keyframe data
                    keyframes = self._parse_keyframes(data_start, bone_name)
                    
                    bone_info = {
                        'index': bone_index,
                        'name': bone_name,
                        'name_offset': name_start,
                        'data_offset': data_start,
                        'keyframes': keyframes
                    }
                    
                    self.bones.append(bone_info)
                    bone_index += 1
                    pos = data_start + 256  # Skip to next potential bone
                else:
                    pos += 1
            else:
                pos += 1
        
        self.animation_info['bone_count'] = len(self.bones)
    
    def _parse_keyframes(self, start_pos, bone_name):
        """Phân tích keyframe data cho một bone"""
        keyframes = []
        
        if start_pos + 128 > len(self.data):
            return keyframes
        
        try:
            # Đọc potential keyframe data
            # Giả sử mỗi keyframe có: time(4), position(12), rotation(16), scale(12) = 44 bytes
            for i in range(0, 128, 44):
                if start_pos + i + 44 > len(self.data):
                    break
                
                # Đọc time
                time_data = self.data[start_pos + i:start_pos + i + 4]
                if len(time_data) == 4:
                    time_val = struct.unpack('<f', time_data)[0]
                    
                    # Đọc position (x, y, z)
                    pos_data = self.data[start_pos + i + 4:start_pos + i + 16]
                    if len(pos_data) == 12:
                        pos_x, pos_y, pos_z = struct.unpack('<fff', pos_data)
                        
                        # Đọc rotation (quaternion: x, y, z, w)
                        rot_data = self.data[start_pos + i + 16:start_pos + i + 32]
                        if len(rot_data) == 16:
                            rot_x, rot_y, rot_z, rot_w = struct.unpack('<ffff', rot_data)
                            
                            # Đọc scale (x, y, z)
                            scale_data = self.data[start_pos + i + 32:start_pos + i + 44]
                            if len(scale_data) == 12:
                                scale_x, scale_y, scale_z = struct.unpack('<fff', scale_data)
                                
                                # Chỉ thêm keyframe nếu có giá trị hợp lý
                                if (abs(time_val) < 1000 and 
                                    abs(pos_x) < 1000 and abs(pos_y) < 1000 and abs(pos_z) < 1000):
                                    
                                    keyframe = {
                                        'time': time_val,
                                        'position': [pos_x, pos_y, pos_z],
                                        'rotation': [rot_x, rot_y, rot_z, rot_w],
                                        'scale': [scale_x, scale_y, scale_z]
                                    }
                                    keyframes.append(keyframe)
        
        except struct.error:
            pass
        
        return keyframes
    
    def export_to_json(self):
        """Export dữ liệu ra file JSON"""
        output_file = self.filename.replace('.ani', '_animation.json')
        
        export_data = {
            'file_info': self.animation_info,
            'bones': self.bones,
            'export_time': datetime.now().isoformat()
        }
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            print(f"✓ Đã export JSON ra {output_file}")
            return output_file
        except Exception as e:
            print(f"✗ Lỗi export JSON: {e}")
            return None
    
    def export_to_csv(self):
        """Export bone list ra file CSV"""
        output_file = self.filename.replace('.ani', '_bones.csv')
        
        try:
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['Index', 'Bone Name', 'Name Offset', 'Data Offset', 'Keyframe Count'])
                
                for bone in self.bones:
                    writer.writerow([
                        bone['index'],
                        bone['name'],
                        f"0x{bone['name_offset']:08X}",
                        f"0x{bone['data_offset']:08X}",
                        len(bone['keyframes'])
                    ])
            
            print(f"✓ Đã export CSV ra {output_file}")
            return output_file
        except Exception as e:
            print(f"✗ Lỗi export CSV: {e}")
            return None
    
    def create_bone_hierarchy_text(self):
        """Tạo file text hiển thị hierarchy của bones"""
        output_file = self.filename.replace('.ani', '_hierarchy.txt')
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"Bone Hierarchy - {self.filename}\n")
                f.write("="*60 + "\n\n")
                
                # Tạo hierarchy tree
                f.write("Bip01 (Root)\n")
                f.write("├── Bip01 Footsteps\n")
                f.write("├── Bip01 Pelvis\n")
                f.write("│   ├── Bip01 Spine\n")
                f.write("│   │   ├── Bip01 Spine1\n")
                f.write("│   │   │   ├── Bip01 Neck\n")
                f.write("│   │   │   │   ├── Bip01 Head\n")
                f.write("│   │   │   │   └── Bip01 Ponytail1\n")
                f.write("│   │   │   ├── Bip01 L Clavicle\n")
                f.write("│   │   │   │   └── Bip01 L UpperArm\n")
                f.write("│   │   │   │       └── Bip01 L Forearm\n")
                f.write("│   │   │   │           └── Bip01 L Hand\n")
                f.write("│   │   │   │               ├── Bip01 L Finger0\n")
                f.write("│   │   │   │               └── Bip01 L Finger01\n")
                f.write("│   │   │   └── Bip01 R Clavicle\n")
                f.write("│   │   │       └── Bip01 R UpperArm\n")
                f.write("│   │   │           └── Bip01 R Forearm\n")
                f.write("│   │   │               └── Bip01 R Hand\n")
                f.write("│   │   │                   ├── Bip01 R Finger0\n")
                f.write("│   │   │                   └── Bip01 R Finger01\n")
                f.write("│   ├── Bip01 L Thigh\n")
                f.write("│   │   └── Bip01 L Calf\n")
                f.write("│   │       └── Bip01 L Foot\n")
                f.write("│   │           └── Bip01 L Toe0\n")
                f.write("│   └── Bip01 R Thigh\n")
                f.write("│       └── Bip01 R Calf\n")
                f.write("│           └── Bip01 R Foot\n")
                f.write("│               └── Bip01 R Toe0\n")
                
                f.write(f"\n\nTổng số bones: {len(self.bones)}\n")
                f.write(f"File size: {self.animation_info['file_size']} bytes\n")
            
            print(f"✓ Đã tạo hierarchy text ra {output_file}")
            return output_file
        except Exception as e:
            print(f"✗ Lỗi tạo hierarchy: {e}")
            return None
    
    def analyze_and_export_all(self):
        """Phân tích và export tất cả định dạng"""
        if not self.load_file():
            return False
        
        self.parse_detailed_structure()
        
        print("\n=== EXPORT FILES ===")
        json_file = self.export_to_json()
        csv_file = self.export_to_csv()
        hierarchy_file = self.create_bone_hierarchy_text()
        
        print(f"\n=== KẾT QUẢ ===")
        print(f"Bones tìm thấy: {len(self.bones)}")
        print(f"Files được tạo:")
        if json_file:
            print(f"  - {json_file} (JSON data)")
        if csv_file:
            print(f"  - {csv_file} (CSV bones)")
        if hierarchy_file:
            print(f"  - {hierarchy_file} (Hierarchy)")
        
        return True

def main():
    if len(sys.argv) != 2:
        print("Sử dụng: python ani_advanced_viewer.py <file.ani>")
        print("Ví dụ: python ani_advanced_viewer.py AMCA0150.ani")
        return
    
    filename = sys.argv[1]
    if not os.path.exists(filename):
        print(f"✗ File không tồn tại: {filename}")
        return
    
    viewer = ANIAdvancedViewer(filename)
    if viewer.analyze_and_export_all():
        print("\n" + "="*60)
        print("PHÂN TÍCH VÀ EXPORT HOÀN TẤT!")
        print("="*60)
        print("\nBạn có thể:")
        print("1. Mở file JSON để xem dữ liệu chi tiết")
        print("2. Mở file CSV trong Excel để xem bảng bones")
        print("3. Xem file hierarchy để hiểu cấu trúc skeleton")
        print("4. Import JSON vào các tool 3D như Blender")

if __name__ == "__main__":
    main()
