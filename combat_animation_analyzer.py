#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Combat Animation Analyzer - Phân tích animation tấn công để tìm damage frames
"""

import struct
import json
import math
import sys
import os

class CombatAnimationAnalyzer:
    def __init__(self, filename):
        self.filename = filename
        self.data = None
        self.bones = []
        self.damage_frames = []
        self.animation_timing = {}
        
    def load_file(self):
        """Đọc file .ani"""
        try:
            with open(self.filename, 'rb') as f:
                self.data = f.read()
            print(f"✓ Đã đọc file {self.filename} ({len(self.data)} bytes)")
            return True
        except Exception as e:
            print(f"✗ Lỗi đọc file: {e}")
            return False
    
    def analyze_timing_structure(self):
        """Phân tích cấu trúc timing của animation"""
        print("\n=== PHÂN TÍCH TIMING ANIMATION ===")
        
        # Tìm các pattern timing trong file
        timing_data = []
        
        # Quét file để tìm các giá trị có thể là frame timing
        for offset in range(0x200, len(self.data) - 4, 4):
            try:
                # Đọc float value
                val = struct.unpack('<f', self.data[offset:offset+4])[0]
                
                # Kiểm tra nếu giá trị có thể là frame time (0-10 giây)
                if 0 <= val <= 10.0 and val != 0:
                    timing_data.append({
                        'offset': offset,
                        'time': val,
                        'frame': int(val * 30)  # Giả sử 30 FPS
                    })
            except:
                continue
        
        # Lọc và sắp xếp timing data
        timing_data = sorted(timing_data, key=lambda x: x['time'])
        
        print(f"Tìm thấy {len(timing_data)} timing values:")
        for i, timing in enumerate(timing_data[:20]):  # Hiển thị 20 đầu tiên
            print(f"  Frame {timing['frame']:3d} (t={timing['time']:.3f}s) tại offset 0x{timing['offset']:08X}")
        
        return timing_data
    
    def analyze_weapon_bones(self):
        """Phân tích movement của bones liên quan đến vũ khí"""
        print("\n=== PHÂN TÍCH WEAPON BONES ===")
        
        # Bones quan trọng cho combat
        weapon_bones = [
            'Bip01 R Hand', 'Bip01 L Hand',
            'Bip01 R UpperArm', 'Bip01 L UpperArm',
            'Bip01 R Forearm', 'Bip01 L Forearm',
            'Bip01 Spine1', 'Bip01 Pelvis'
        ]
        
        weapon_movements = []
        
        # Tìm và phân tích movement data cho weapon bones
        for bone_name in weapon_bones:
            movement_data = self._analyze_bone_movement(bone_name)
            if movement_data:
                weapon_movements.append({
                    'bone': bone_name,
                    'movements': movement_data
                })
        
        return weapon_movements
    
    def _analyze_bone_movement(self, bone_name):
        """Phân tích movement của một bone cụ thể"""
        # Tìm bone trong file
        bone_pos = self.data.find(bone_name.encode('ascii'))
        if bone_pos == -1:
            return None
        
        # Tìm animation data sau tên bone
        data_start = bone_pos + len(bone_name) + 1
        while data_start % 16 != 0:
            data_start += 1
        
        movements = []
        
        # Đọc potential movement data
        for i in range(0, 200, 16):  # Đọc 200 bytes, mỗi keyframe 16 bytes
            if data_start + i + 16 > len(self.data):
                break
            
            try:
                # Đọc position/rotation data
                x, y, z, w = struct.unpack('<ffff', self.data[data_start + i:data_start + i + 16])
                
                # Tính velocity (tốc độ di chuyển)
                velocity = math.sqrt(x*x + y*y + z*z)
                
                if velocity > 0.01:  # Chỉ lấy movement có ý nghĩa
                    movements.append({
                        'frame': i // 16,
                        'position': [x, y, z],
                        'velocity': velocity,
                        'offset': data_start + i
                    })
            except:
                continue
        
        return movements
    
    def detect_damage_frames(self, weapon_movements, timing_data):
        """Phát hiện damage frames dựa trên movement patterns"""
        print("\n=== PHÁT HIỆN DAMAGE FRAMES ===")
        
        damage_candidates = []
        
        # Phân tích peak velocity của weapon bones
        for weapon in weapon_movements:
            bone_name = weapon['bone']
            movements = weapon['movements']
            
            if not movements:
                continue
            
            # Tìm peak velocity
            max_velocity = max(m['velocity'] for m in movements)
            
            for movement in movements:
                velocity_ratio = movement['velocity'] / max_velocity if max_velocity > 0 else 0
                
                # Damage frame thường ở peak velocity (>70% max velocity)
                if velocity_ratio > 0.7:
                    damage_candidates.append({
                        'bone': bone_name,
                        'frame': movement['frame'],
                        'velocity': movement['velocity'],
                        'velocity_ratio': velocity_ratio,
                        'position': movement['position'],
                        'confidence': velocity_ratio * 100
                    })
        
        # Sắp xếp theo confidence
        damage_candidates = sorted(damage_candidates, key=lambda x: x['confidence'], reverse=True)
        
        print("Damage frame candidates (sắp xếp theo confidence):")
        for i, candidate in enumerate(damage_candidates[:10]):
            print(f"  {i+1:2d}. Frame {candidate['frame']:3d} - {candidate['bone']}")
            print(f"      Velocity: {candidate['velocity']:.3f} ({candidate['velocity_ratio']:.1%} of max)")
            print(f"      Confidence: {candidate['confidence']:.1f}%")
            print()
        
        return damage_candidates
    
    def estimate_animation_fps(self):
        """Ước tính FPS của animation"""
        print("\n=== ƯỚC TÍNH ANIMATION FPS ===")
        
        # Tìm các timing values liên tiếp
        timing_values = []
        for offset in range(0x200, len(self.data) - 4, 4):
            try:
                val = struct.unpack('<f', self.data[offset:offset+4])[0]
                if 0 < val < 5.0:  # Timing hợp lý
                    timing_values.append(val)
            except:
                continue
        
        if len(timing_values) < 2:
            print("Không đủ dữ liệu để ước tính FPS")
            return 30  # Default 30 FPS
        
        # Tính frame intervals
        timing_values = sorted(set(timing_values))
        intervals = []
        for i in range(1, len(timing_values)):
            interval = timing_values[i] - timing_values[i-1]
            if 0.01 < interval < 1.0:  # Interval hợp lý
                intervals.append(interval)
        
        if intervals:
            avg_interval = sum(intervals) / len(intervals)
            estimated_fps = 1.0 / avg_interval
            print(f"Estimated FPS: {estimated_fps:.1f}")
            print(f"Average frame interval: {avg_interval:.4f}s")
            return estimated_fps
        
        return 30  # Default
    
    def create_damage_timing_report(self, damage_candidates, fps=30):
        """Tạo báo cáo timing cho damage frames"""
        output_file = self.filename.replace('.ani', '_damage_timing.txt')
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"DAMAGE TIMING ANALYSIS - {self.filename}\n")
                f.write("="*60 + "\n\n")
                
                f.write(f"Estimated FPS: {fps:.1f}\n")
                f.write(f"Animation Type: Combat/Attack\n\n")
                
                f.write("DAMAGE FRAME CANDIDATES:\n")
                f.write("-" * 40 + "\n")
                
                for i, candidate in enumerate(damage_candidates[:5]):
                    frame_time = candidate['frame'] / fps
                    f.write(f"{i+1}. Frame {candidate['frame']} (t={frame_time:.3f}s)\n")
                    f.write(f"   Bone: {candidate['bone']}\n")
                    f.write(f"   Velocity: {candidate['velocity']:.3f}\n")
                    f.write(f"   Confidence: {candidate['confidence']:.1f}%\n")
                    f.write(f"   Position: [{candidate['position'][0]:.3f}, {candidate['position'][1]:.3f}, {candidate['position'][2]:.3f}]\n\n")
                
                f.write("\nRECOMMENDATIONS:\n")
                f.write("-" * 20 + "\n")
                if damage_candidates:
                    best_frame = damage_candidates[0]['frame']
                    best_time = best_frame / fps
                    f.write(f"• Primary damage frame: {best_frame} (t={best_time:.3f}s)\n")
                    f.write(f"• Bone to monitor: {damage_candidates[0]['bone']}\n")
                    f.write(f"• Trigger damage when this bone reaches peak velocity\n")
                else:
                    f.write("• No clear damage frames detected\n")
                    f.write("• Manual analysis may be required\n")
            
            print(f"✓ Đã tạo damage timing report: {output_file}")
            return output_file
        except Exception as e:
            print(f"✗ Lỗi tạo report: {e}")
            return None
    
    def analyze_combat_animation(self):
        """Phân tích toàn bộ combat animation"""
        if not self.load_file():
            return False
        
        print("🗡️  COMBAT ANIMATION ANALYZER")
        print("="*50)
        
        # 1. Phân tích timing structure
        timing_data = self.analyze_timing_structure()
        
        # 2. Ước tính FPS
        fps = self.estimate_animation_fps()
        
        # 3. Phân tích weapon bones
        weapon_movements = self.analyze_weapon_bones()
        
        # 4. Phát hiện damage frames
        damage_candidates = self.detect_damage_frames(weapon_movements, timing_data)
        
        # 5. Tạo report
        report_file = self.create_damage_timing_report(damage_candidates, fps)
        
        print("\n" + "="*50)
        print("🎯 KẾT LUẬN:")
        if damage_candidates:
            best_candidate = damage_candidates[0]
            frame_time = best_candidate['frame'] / fps
            print(f"• Damage frame khả năng cao nhất: Frame {best_candidate['frame']}")
            print(f"• Thời gian: {frame_time:.3f} giây từ đầu animation")
            print(f"• Bone quan trọng: {best_candidate['bone']}")
            print(f"• Confidence: {best_candidate['confidence']:.1f}%")
        else:
            print("• Không phát hiện được damage frame rõ ràng")
            print("• Cần phân tích thủ công hoặc thêm dữ liệu")
        
        return True

def main():
    if len(sys.argv) != 2:
        print("Sử dụng: python combat_animation_analyzer.py <file.ani>")
        print("Ví dụ: python combat_animation_analyzer.py AMCA0150.ani")
        return
    
    filename = sys.argv[1]
    if not os.path.exists(filename):
        print(f"✗ File không tồn tại: {filename}")
        return
    
    analyzer = CombatAnimationAnalyzer(filename)
    analyzer.analyze_combat_animation()

if __name__ == "__main__":
    main()
