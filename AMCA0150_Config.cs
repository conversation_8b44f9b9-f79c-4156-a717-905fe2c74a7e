using UnityEngine;
using System.Collections.Generic;

// Auto-generated damage config for AMCA0150
// Generated by ANI Combat Analyzer

[CreateAssetMenu(fileName = "AMCA0150_Config", menuName = "Combat/Animation Config")]
public class Attack0150Config : ScriptableObject
{
    [Header("Animation Settings")]
    public float animationFPS = 6.7f;
    public float totalDuration = 2.5f;
    
    [Header("Damage Events")]
    public List<DamageEvent> damageEvents = new List<DamageEvent>();
    
    [Header("Hit Detection")]
    public float velocityThreshold = 1000.0f;
    public float detectionRadius = 2.0f;
    public LayerMask targetLayers;
    
    [Header("Combo Settings")]
    public int comboFrame = 15;
    public float comboWindow = 0.5f;
    
    [Header("Cancel Settings")]
    public int cancelFrame = 8;
    public int cancelCost = 10;
    
    void OnEnable()
    {
        InitializeDamageEvents();
    }
    
    void InitializeDamageEvents()
    {
        damageEvents.Clear();
        
        // Primary damage event
        damageEvents.Add(new DamageEvent
        {
            frame = 12,
            time = 1.799f,
            boneName = "Bip01 R Hand",
            damageMultiplier = 1.0f,
            knockbackForce = 500,
            soundEffect = "sword_hit_01.wav",
            particleEffect = "slash_effect_01"
        });
        
        // Secondary damage event
        damageEvents.Add(new DamageEvent
        {
            frame = 6,
            time = 0.9f,
            boneName = "Bip01 L Hand",
            damageMultiplier = 0.7f,
            knockbackForce = 300,
            soundEffect = "sword_hit_02.wav",
            particleEffect = "slash_effect_02"
        });
    }
}

[System.Serializable]
public class DamageEvent
{
    public int frame;
    public float time;
    public string boneName;
    public float damageMultiplier = 1.0f;
    public float knockbackForce = 500f;
    public string soundEffect;
    public string particleEffect;
    public Vector3 hitBoxSize = Vector3.one;
}