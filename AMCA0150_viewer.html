<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ANI Viewer - AMCA0150.ani</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #2196F3, #21CBF3);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
        }
        .info-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 5px solid #2196F3;
        }
        .info-card h3 {
            margin-top: 0;
            color: #2196F3;
        }
        .bone-list {
            padding: 30px;
        }
        .bone-item {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 10px 0;
            padding: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .bone-item:hover {
            background: #f0f8ff;
            border-color: #2196F3;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.2);
        }
        .bone-name {
            font-weight: bold;
            color: #2196F3;
            font-size: 1.1em;
        }
        .bone-details {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
        }
        .keyframe-count {
            background: #4CAF50;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            display: inline-block;
            margin-left: 10px;
        }
        .hierarchy {
            background: #f8f9fa;
            padding: 30px;
            font-family: 'Courier New', monospace;
            white-space: pre-line;
            border-top: 1px solid #ddd;
        }
        .search-box {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
        }
        .search-box input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }
        .search-box input:focus {
            border-color: #2196F3;
        }
        .tab-container {
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
        }
        .tab-buttons {
            display: flex;
            padding: 0 30px;
        }
        .tab-button {
            background: none;
            border: none;
            padding: 15px 25px;
            cursor: pointer;
            font-size: 16px;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        .tab-button.active {
            color: #2196F3;
            border-bottom-color: #2196F3;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 ANI File Viewer</h1>
            <p>File: AMCA0150.ani</p>
        </div>
        
        <div class="tab-container">
            <div class="tab-buttons">
                <button class="tab-button active" onclick="showTab('info')">📊 Thông tin</button>
                <button class="tab-button" onclick="showTab('bones')">🦴 Bones</button>
                <button class="tab-button" onclick="showTab('hierarchy')">🌳 Hierarchy</button>
            </div>
        </div>
        
        <div id="info-tab" class="tab-content active">
            <div class="info-grid">
                <div class="info-card">
                    <h3>📁 File Info</h3>
                    <p><strong>Kích thước:</strong> 23,333 bytes</p>
                    <p><strong>Signature:</strong> FBip01</p>
                    <p><strong>Header size:</strong> 512 bytes</p>
                </div>
                <div class="info-card">
                    <h3>🦴 Skeleton Info</h3>
                    <p><strong>Số bones:</strong> 16</p>
                    <p><strong>Keyframes:</strong> 0</p>
                    <p><strong>Animation length:</strong> 0</p>
                </div>
                <div class="info-card">
                    <h3>🎯 Bone Types</h3>
                    <p><strong>Root:</strong> Bip01</p>
                    <p><strong>Body:</strong> Pelvis, Spine, Neck, Head</p>
                    <p><strong>Arms:</strong> Clavicle, UpperArm, Forearm, Hand</p>
                    <p><strong>Legs:</strong> Thigh, Calf, Foot, Toe</p>
                </div>
            </div>
        </div>
        
        <div id="bones-tab" class="tab-content">
            <div class="search-box">
                <input type="text" id="bone-search" placeholder="🔍 Tìm kiếm bone..." onkeyup="filterBones()">
            </div>
            <div class="bone-list" id="bone-list">

                <div class="bone-item" data-name="bip01">
                    <div class="bone-name">
                        Bip01
                        <span class="keyframe-count">3 keyframes</span>
                    </div>
                    <div class="bone-details">
                        Index: 0 | 
                        Name Offset: 0x00000201 | 
                        Data Offset: 0x00000210
                    </div>
                </div>
                <div class="bone-item" data-name="bip01 footsteps">
                    <div class="bone-name">
                        Bip01 Footsteps
                        <span class="keyframe-count">2 keyframes</span>
                    </div>
                    <div class="bone-details">
                        Index: 1 | 
                        Name Offset: 0x00000377 | 
                        Data Offset: 0x00000390
                    </div>
                </div>
                <div class="bone-item" data-name="bip01 pelvis">
                    <div class="bone-name">
                        Bip01 Pelvis
                        <span class="keyframe-count">3 keyframes</span>
                    </div>
                    <div class="bone-details">
                        Index: 2 | 
                        Name Offset: 0x000004FD | 
                        Data Offset: 0x00000510
                    </div>
                </div>
                <div class="bone-item" data-name="bip01 spine1">
                    <div class="bone-name">
                        Bip01 Spine1
                        <span class="keyframe-count">2 keyframes</span>
                    </div>
                    <div class="bone-details">
                        Index: 3 | 
                        Name Offset: 0x0000067D | 
                        Data Offset: 0x00000690
                    </div>
                </div>
                <div class="bone-item" data-name="bip01 head">
                    <div class="bone-name">
                        Bip01 Head
                        <span class="keyframe-count">3 keyframes</span>
                    </div>
                    <div class="bone-details">
                        Index: 4 | 
                        Name Offset: 0x000007FD | 
                        Data Offset: 0x00000810
                    </div>
                </div>
                <div class="bone-item" data-name="bip01 ponytail1">
                    <div class="bone-name">
                        Bip01 Ponytail1
                        <span class="keyframe-count">2 keyframes</span>
                    </div>
                    <div class="bone-details">
                        Index: 5 | 
                        Name Offset: 0x00000953 | 
                        Data Offset: 0x00000970
                    </div>
                </div>
                <div class="bone-item" data-name="bip01 l clavicle">
                    <div class="bone-name">
                        Bip01 L Clavicle
                        <span class="keyframe-count">1 keyframes</span>
                    </div>
                    <div class="bone-details">
                        Index: 6 | 
                        Name Offset: 0x00001BF1 | 
                        Data Offset: 0x00001C10
                    </div>
                </div>
                <div class="bone-item" data-name="bip01 l forearm">
                    <div class="bone-name">
                        Bip01 L Forearm
                        <span class="keyframe-count">1 keyframes</span>
                    </div>
                    <div class="bone-details">
                        Index: 7 | 
                        Name Offset: 0x00001D71 | 
                        Data Offset: 0x00001D90
                    </div>
                </div>
                <div class="bone-item" data-name="bip01 l finger0">
                    <div class="bone-name">
                        Bip01 L Finger0
                        <span class="keyframe-count">2 keyframes</span>
                    </div>
                    <div class="bone-details">
                        Index: 8 | 
                        Name Offset: 0x00001F7D | 
                        Data Offset: 0x00001F90
                    </div>
                </div>
                <div class="bone-item" data-name="bip01 r clavicle">
                    <div class="bone-name">
                        Bip01 R Clavicle
                        <span class="keyframe-count">2 keyframes</span>
                    </div>
                    <div class="bone-details">
                        Index: 9 | 
                        Name Offset: 0x00002189 | 
                        Data Offset: 0x000021A0
                    </div>
                </div>
                <div class="bone-item" data-name="bip01 r forearm">
                    <div class="bone-name">
                        Bip01 R Forearm
                        <span class="keyframe-count">1 keyframes</span>
                    </div>
                    <div class="bone-details">
                        Index: 10 | 
                        Name Offset: 0x00002359 | 
                        Data Offset: 0x00002370
                    </div>
                </div>
                <div class="bone-item" data-name="bip01 r finger0">
                    <div class="bone-name">
                        Bip01 R Finger0
                        <span class="keyframe-count">2 keyframes</span>
                    </div>
                    <div class="bone-details">
                        Index: 11 | 
                        Name Offset: 0x00002565 | 
                        Data Offset: 0x00002580
                    </div>
                </div>
                <div class="bone-item" data-name="bip01 l thigh">
                    <div class="bone-name">
                        Bip01 L Thigh
                        <span class="keyframe-count">1 keyframes</span>
                    </div>
                    <div class="bone-details">
                        Index: 12 | 
                        Name Offset: 0x00003635 | 
                        Data Offset: 0x00003650
                    </div>
                </div>
                <div class="bone-item" data-name="bip01 l foot">
                    <div class="bone-name">
                        Bip01 L Foot
                        <span class="keyframe-count">2 keyframes</span>
                    </div>
                    <div class="bone-details">
                        Index: 13 | 
                        Name Offset: 0x00003841 | 
                        Data Offset: 0x00003850
                    </div>
                </div>
                <div class="bone-item" data-name="bip01 r thigh">
                    <div class="bone-name">
                        Bip01 R Thigh
                        <span class="keyframe-count">2 keyframes</span>
                    </div>
                    <div class="bone-details">
                        Index: 14 | 
                        Name Offset: 0x00003A4D | 
                        Data Offset: 0x00003A60
                    </div>
                </div>
                <div class="bone-item" data-name="bip01 r foot">
                    <div class="bone-name">
                        Bip01 R Foot
                        <span class="keyframe-count">2 keyframes</span>
                    </div>
                    <div class="bone-details">
                        Index: 15 | 
                        Name Offset: 0x00003C59 | 
                        Data Offset: 0x00003C70
                    </div>
                </div>
            </div>
        </div>
        
        <div id="hierarchy-tab" class="tab-content">
            <div class="hierarchy">
Bip01 (Root)
├── Bip01 Footsteps
├── Bip01 Pelvis
│   ├── Bip01 Spine
│   │   ├── Bip01 Spine1
│   │   │   ├── Bip01 Neck
│   │   │   │   ├── Bip01 Head
│   │   │   │   └── Bip01 Ponytail1
│   │   │   ├── Bip01 L Clavicle
│   │   │   │   └── Bip01 L UpperArm
│   │   │   │       └── Bip01 L Forearm
│   │   │   │           └── Bip01 L Hand
│   │   │   │               ├── Bip01 L Finger0
│   │   │   │               └── Bip01 L Finger01
│   │   │   └── Bip01 R Clavicle
│   │   │       └── Bip01 R UpperArm
│   │   │           └── Bip01 R Forearm
│   │   │               └── Bip01 R Hand
│   │   │                   ├── Bip01 R Finger0
│   │   │                   └── Bip01 R Finger01
│   ├── Bip01 L Thigh
│   │   └── Bip01 L Calf
│   │       └── Bip01 L Foot
│   │           └── Bip01 L Toe0
│   └── Bip01 R Thigh
│       └── Bip01 R Calf
│           └── Bip01 R Foot
│               └── Bip01 R Toe0
            </div>
        </div>
    </div>
    
    <script>
        function showTab(tabName) {
            // Ẩn tất cả tabs
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // Bỏ active từ tất cả buttons
            const buttons = document.querySelectorAll('.tab-button');
            buttons.forEach(btn => btn.classList.remove('active'));
            
            // Hiển thị tab được chọn
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
        }
        
        function filterBones() {
            const searchTerm = document.getElementById('bone-search').value.toLowerCase();
            const bones = document.querySelectorAll('.bone-item');
            
            bones.forEach(bone => {
                const boneName = bone.getAttribute('data-name');
                if (boneName.includes(searchTerm)) {
                    bone.style.display = 'block';
                } else {
                    bone.style.display = 'none';
                }
            });
        }
        
        // Thêm click event cho bones
        document.querySelectorAll('.bone-item').forEach(bone => {
            bone.addEventListener('click', function() {
                const boneName = this.querySelector('.bone-name').textContent.split('\n')[0];
                alert('Bone: ' + boneName + '\n\nClick để xem chi tiết keyframes (tính năng sẽ được thêm)');
            });
        });
    </script>
</body>
</html>