// Auto-generated damage config for AMCA0150
// Generated by ANI Combat Analyzer

#ifndef AMCA0150_DAMAGE_CONFIG_H
#define AMCA0150_DAMAGE_CONFIG_H

#include "AnimationConfig.h"
#include "DamageEvent.h"

namespace Combat {

class AMCA0150Config : public AnimationConfig {
public:
    static constexpr float ANIMATION_FPS = 6.7f;
    static constexpr float TOTAL_DURATION = 2.5f;
    
    // Damage Events
    static constexpr int PRIMARY_DAMAGE_FRAME = 12;
    static constexpr float PRIMARY_DAMAGE_TIME = 1.799f;
    static constexpr float PRIMARY_DAMAGE_MULTIPLIER = 1.0f;
    
    static constexpr int SECONDARY_DAMAGE_FRAME = 6;
    static constexpr float SECONDARY_DAMAGE_TIME = 0.9f;
    static constexpr float SECONDARY_DAMAGE_MULTIPLIER = 0.7f;
    
    // Hit Detection
    static constexpr float VELOCITY_THRESHOLD = 1000.0f;
    static constexpr float DETECTION_RADIUS = 2.0f;
    
    // Combo Data
    static constexpr int COMBO_FRAME = 15;
    static constexpr float COMBO_WINDOW = 0.5f;
    
    // Cancel Data
    static constexpr int CANCEL_FRAME = 8;
    static constexpr int CANCEL_COST = 10;
    
    void InitializeDamageEvents() override {
        // Primary damage event
        DamageEvent primaryEvent;
        primaryEvent.frame = PRIMARY_DAMAGE_FRAME;
        primaryEvent.time = PRIMARY_DAMAGE_TIME;
        primaryEvent.boneName = "Bip01 R Hand";
        primaryEvent.damageMultiplier = PRIMARY_DAMAGE_MULTIPLIER;
        primaryEvent.knockbackForce = 500;
        AddDamageEvent(primaryEvent);
        
        // Secondary damage event
        DamageEvent secondaryEvent;
        secondaryEvent.frame = SECONDARY_DAMAGE_FRAME;
        secondaryEvent.time = SECONDARY_DAMAGE_TIME;
        secondaryEvent.boneName = "Bip01 L Hand";
        secondaryEvent.damageMultiplier = SECONDARY_DAMAGE_MULTIPLIER;
        secondaryEvent.knockbackForce = 300;
        AddDamageEvent(secondaryEvent);
    }
};

} // namespace Combat

#endif // AMCA0150_DAMAGE_CONFIG_H