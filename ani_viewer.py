#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANI File Viewer - Tool để phân tích file animation .ani của game 3D
Hỗ trợ đọc và hiển thị thông tin về bones, keyframes và animation data
"""

import struct
import sys
import os

class ANIViewer:
    def __init__(self, filename):
        self.filename = filename
        self.data = None
        self.bones = []
        self.animations = []
        
    def load_file(self):
        """Đọc file .ani"""
        try:
            with open(self.filename, 'rb') as f:
                self.data = f.read()
            print(f"✓ Đã đọc file {self.filename} ({len(self.data)} bytes)")
            return True
        except Exception as e:
            print(f"✗ Lỗi đọc file: {e}")
            return False
    
    def parse_header(self):
        """Phân tích header của file"""
        print("\n=== HEADER ANALYSIS ===")
        print(f"File size: {len(self.data)} bytes")
        
        # <PERSON><PERSON>m tra 512 bytes đầu (0x200)
        header_zeros = self.data[:0x200]
        if all(b == 0 for b in header_zeros):
            print("✓ Header: 512 bytes đầu là 0x00 (như mong đợi)")
        else:
            print("⚠ Header: Có dữ liệu khác 0x00 trong 512 bytes đầu")
        
        # Tìm signature đầu tiên
        pos = 0x200
        if pos < len(self.data):
            signature = self.data[pos:pos+16].decode('ascii', errors='ignore').rstrip('\x00')
            print(f"✓ Signature tại 0x{pos:X}: '{signature}'")
    
    def find_bone_names(self):
        """Tìm và extract tên các bones"""
        print("\n=== BONE NAMES ===")
        
        # Tìm các chuỗi có chứa "Bip01"
        pos = 0
        bone_count = 0
        
        while pos < len(self.data) - 32:
            # Tìm "Bip01" hoặc các bone names khác
            if self.data[pos:pos+5] == b'Bip01':
                # Đọc tên bone (giả sử tối đa 32 chars)
                bone_name = ""
                for i in range(32):
                    if pos + i >= len(self.data):
                        break
                    char = self.data[pos + i]
                    if char == 0:
                        break
                    if 32 <= char <= 126:  # Printable ASCII
                        bone_name += chr(char)
                    else:
                        break
                
                if len(bone_name) > 4:  # Chỉ lấy tên có ý nghĩa
                    print(f"  [{bone_count:2d}] 0x{pos:08X}: {bone_name}")
                    self.bones.append({
                        'name': bone_name,
                        'offset': pos,
                        'index': bone_count
                    })
                    bone_count += 1
                    pos += len(bone_name) + 1
                else:
                    pos += 1
            else:
                pos += 1
        
        print(f"\n✓ Tìm thấy {len(self.bones)} bones")
    
    def analyze_animation_data(self):
        """Phân tích dữ liệu animation"""
        print("\n=== ANIMATION DATA ANALYSIS ===")
        
        for i, bone in enumerate(self.bones):
            print(f"\n--- Bone {i}: {bone['name']} ---")
            
            # Tìm dữ liệu animation sau tên bone
            start_pos = bone['offset'] + len(bone['name']) + 1
            
            # Align to next 16-byte boundary
            while start_pos % 16 != 0:
                start_pos += 1
            
            if start_pos + 64 < len(self.data):
                # Đọc một số float values (có thể là transform matrix hoặc keyframe data)
                try:
                    # Đọc 16 floats (64 bytes) - có thể là 4x4 matrix
                    floats = []
                    for j in range(16):
                        if start_pos + j*4 + 4 <= len(self.data):
                            val = struct.unpack('<f', self.data[start_pos + j*4:start_pos + j*4 + 4])[0]
                            floats.append(val)
                    
                    if len(floats) >= 16:
                        print(f"  Transform Matrix (4x4):")
                        for row in range(4):
                            row_data = floats[row*4:(row+1)*4]
                            print(f"    [{row_data[0]:8.3f} {row_data[1]:8.3f} {row_data[2]:8.3f} {row_data[3]:8.3f}]")
                
                except struct.error:
                    print(f"  ⚠ Không thể đọc dữ liệu float tại 0x{start_pos:X}")
    
    def export_bone_list(self):
        """Export danh sách bones ra file text"""
        output_file = self.filename.replace('.ani', '_bones.txt')
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"Bone list from {self.filename}\n")
                f.write("="*50 + "\n\n")
                
                for i, bone in enumerate(self.bones):
                    f.write(f"{i:2d}. {bone['name']} (offset: 0x{bone['offset']:08X})\n")
            
            print(f"\n✓ Đã export danh sách bones ra {output_file}")
        except Exception as e:
            print(f"✗ Lỗi export: {e}")
    
    def hex_dump(self, start_offset=0, length=256):
        """Hiển thị hex dump của một phần file"""
        print(f"\n=== HEX DUMP (0x{start_offset:X} - 0x{start_offset+length:X}) ===")
        
        for i in range(0, length, 16):
            if start_offset + i >= len(self.data):
                break
                
            offset = start_offset + i
            hex_part = ""
            ascii_part = ""
            
            for j in range(16):
                if offset + j < len(self.data):
                    byte_val = self.data[offset + j]
                    hex_part += f"{byte_val:02X} "
                    ascii_part += chr(byte_val) if 32 <= byte_val <= 126 else "."
                else:
                    hex_part += "   "
                    ascii_part += " "
            
            print(f"{offset:08X}: {hex_part} | {ascii_part}")
    
    def analyze(self):
        """Phân tích toàn bộ file"""
        if not self.load_file():
            return False
        
        self.parse_header()
        self.find_bone_names()
        self.analyze_animation_data()
        self.export_bone_list()
        
        return True

def main():
    if len(sys.argv) != 2:
        print("Sử dụng: python ani_viewer.py <file.ani>")
        print("Ví dụ: python ani_viewer.py AMCA0150.ani")
        return
    
    filename = sys.argv[1]
    if not os.path.exists(filename):
        print(f"✗ File không tồn tại: {filename}")
        return
    
    viewer = ANIViewer(filename)
    if viewer.analyze():
        print("\n" + "="*60)
        print("PHÂN TÍCH HOÀN TẤT!")
        print("="*60)
        
        # Hiển thị menu tương tác
        while True:
            print("\nTùy chọn:")
            print("1. Hiển thị hex dump tại vị trí cụ thể")
            print("2. Tìm kiếm chuỗi trong file")
            print("3. Phân tích bone cụ thể")
            print("4. Thoát")
            
            choice = input("\nChọn (1-4): ").strip()
            
            if choice == '1':
                try:
                    offset = int(input("Nhập offset (hex, ví dụ: 0x200): "), 16)
                    length = int(input("Nhập độ dài (decimal, ví dụ: 256): "))
                    viewer.hex_dump(offset, length)
                except ValueError:
                    print("✗ Giá trị không hợp lệ")
            
            elif choice == '2':
                search_str = input("Nhập chuỗi cần tìm: ")
                search_bytes = search_str.encode('ascii', errors='ignore')
                pos = viewer.data.find(search_bytes)
                if pos != -1:
                    print(f"✓ Tìm thấy '{search_str}' tại offset 0x{pos:X}")
                    viewer.hex_dump(max(0, pos-32), 128)
                else:
                    print(f"✗ Không tìm thấy '{search_str}'")
            
            elif choice == '3':
                try:
                    bone_idx = int(input(f"Nhập index bone (0-{len(viewer.bones)-1}): "))
                    if 0 <= bone_idx < len(viewer.bones):
                        bone = viewer.bones[bone_idx]
                        print(f"\nBone: {bone['name']}")
                        viewer.hex_dump(bone['offset'], 256)
                    else:
                        print("✗ Index không hợp lệ")
                except ValueError:
                    print("✗ Giá trị không hợp lệ")
            
            elif choice == '4':
                break
            
            else:
                print("✗ Lựa chọn không hợp lệ")

if __name__ == "__main__":
    main()
