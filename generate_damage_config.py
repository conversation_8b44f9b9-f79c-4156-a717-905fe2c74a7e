#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Generate Damage Config - Tạo file config cho game engine để implement damage timing
"""

import json
import sys
import os

def create_damage_config(ani_file):
    """Tạo file config cho damage timing"""
    
    # <PERSON><PERSON> liệu từ phân tích
    config = {
        "animation_name": ani_file.replace('.ani', ''),
        "animation_type": "combat_attack",
        "fps": 6.7,
        "total_duration": 2.5,  # Ước tính
        
        "damage_events": [
            {
                "frame": 12,
                "time": 1.799,
                "bone": "Bip01 R Hand",
                "damage_type": "primary",
                "damage_multiplier": 1.0,
                "hit_box_size": [1.5, 1.5, 1.5],
                "knockback_force": 500,
                "sound_effect": "sword_hit_01.wav",
                "particle_effect": "slash_effect_01"
            },
            {
                "frame": 6,
                "time": 0.900,
                "bone": "Bip01 L Hand",
                "damage_type": "secondary",
                "damage_multiplier": 0.7,
                "hit_box_size": [1.2, 1.2, 1.2],
                "knockback_force": 300,
                "sound_effect": "sword_hit_02.wav",
                "particle_effect": "slash_effect_02"
            },
            {
                "frame": 9,
                "time": 1.349,
                "bone": "Bip01 R UpperArm",
                "damage_type": "combo",
                "damage_multiplier": 0.8,
                "hit_box_size": [1.0, 1.0, 1.0],
                "knockback_force": 400,
                "sound_effect": "sword_hit_03.wav",
                "particle_effect": "slash_effect_03"
            }
        ],
        
        "hit_detection": {
            "method": "bone_velocity_threshold",
            "velocity_threshold": 1000.0,
            "detection_radius": 2.0,
            "target_layers": ["enemy", "destructible"],
            "ignore_layers": ["player", "npc_friendly"]
        },
        
        "animation_events": [
            {
                "frame": 0,
                "time": 0.0,
                "event": "animation_start",
                "sound": "sword_draw.wav"
            },
            {
                "frame": 3,
                "time": 0.45,
                "event": "wind_up_complete",
                "sound": "sword_whoosh.wav"
            },
            {
                "frame": 12,
                "time": 1.799,
                "event": "damage_peak",
                "sound": "sword_impact.wav"
            },
            {
                "frame": 18,
                "time": 2.5,
                "event": "animation_end",
                "sound": "sword_sheath.wav"
            }
        ],
        
        "combo_data": {
            "can_combo_after_frame": 15,
            "combo_window": 0.5,
            "next_possible_attacks": ["AMCA0151", "AMCA0152", "AMCA0153"]
        },
        
        "cancel_data": {
            "can_cancel_after_frame": 8,
            "cancel_into": ["dodge", "block", "parry"],
            "cancel_cost": 10  # stamina cost
        }
    }
    
    return config

def create_cpp_header(config):
    """Tạo C++ header file cho game engine"""
    ani_name = config['animation_name'].upper()
    
    cpp_content = f"""// Auto-generated damage config for {config['animation_name']}
// Generated by ANI Combat Analyzer

#ifndef {ani_name}_DAMAGE_CONFIG_H
#define {ani_name}_DAMAGE_CONFIG_H

#include "AnimationConfig.h"
#include "DamageEvent.h"

namespace Combat {{

class {config['animation_name']}Config : public AnimationConfig {{
public:
    static constexpr float ANIMATION_FPS = {config['fps']}f;
    static constexpr float TOTAL_DURATION = {config['total_duration']}f;
    
    // Damage Events
    static constexpr int PRIMARY_DAMAGE_FRAME = {config['damage_events'][0]['frame']};
    static constexpr float PRIMARY_DAMAGE_TIME = {config['damage_events'][0]['time']}f;
    static constexpr float PRIMARY_DAMAGE_MULTIPLIER = {config['damage_events'][0]['damage_multiplier']}f;
    
    static constexpr int SECONDARY_DAMAGE_FRAME = {config['damage_events'][1]['frame']};
    static constexpr float SECONDARY_DAMAGE_TIME = {config['damage_events'][1]['time']}f;
    static constexpr float SECONDARY_DAMAGE_MULTIPLIER = {config['damage_events'][1]['damage_multiplier']}f;
    
    // Hit Detection
    static constexpr float VELOCITY_THRESHOLD = {config['hit_detection']['velocity_threshold']}f;
    static constexpr float DETECTION_RADIUS = {config['hit_detection']['detection_radius']}f;
    
    // Combo Data
    static constexpr int COMBO_FRAME = {config['combo_data']['can_combo_after_frame']};
    static constexpr float COMBO_WINDOW = {config['combo_data']['combo_window']}f;
    
    // Cancel Data
    static constexpr int CANCEL_FRAME = {config['cancel_data']['can_cancel_after_frame']};
    static constexpr int CANCEL_COST = {config['cancel_data']['cancel_cost']};
    
    void InitializeDamageEvents() override {{
        // Primary damage event
        DamageEvent primaryEvent;
        primaryEvent.frame = PRIMARY_DAMAGE_FRAME;
        primaryEvent.time = PRIMARY_DAMAGE_TIME;
        primaryEvent.boneName = "{config['damage_events'][0]['bone']}";
        primaryEvent.damageMultiplier = PRIMARY_DAMAGE_MULTIPLIER;
        primaryEvent.knockbackForce = {config['damage_events'][0]['knockback_force']};
        AddDamageEvent(primaryEvent);
        
        // Secondary damage event
        DamageEvent secondaryEvent;
        secondaryEvent.frame = SECONDARY_DAMAGE_FRAME;
        secondaryEvent.time = SECONDARY_DAMAGE_TIME;
        secondaryEvent.boneName = "{config['damage_events'][1]['bone']}";
        secondaryEvent.damageMultiplier = SECONDARY_DAMAGE_MULTIPLIER;
        secondaryEvent.knockbackForce = {config['damage_events'][1]['knockback_force']};
        AddDamageEvent(secondaryEvent);
    }}
}};

}} // namespace Combat

#endif // {ani_name}_DAMAGE_CONFIG_H"""
    
    return cpp_content

def create_unity_script(config):
    """Tạo Unity C# script"""
    class_name = config['animation_name'].replace('AMCA', 'Attack')
    
    unity_content = f"""using UnityEngine;
using System.Collections.Generic;

// Auto-generated damage config for {config['animation_name']}
// Generated by ANI Combat Analyzer

[CreateAssetMenu(fileName = "{config['animation_name']}_Config", menuName = "Combat/Animation Config")]
public class {class_name}Config : ScriptableObject
{{
    [Header("Animation Settings")]
    public float animationFPS = {config['fps']}f;
    public float totalDuration = {config['total_duration']}f;
    
    [Header("Damage Events")]
    public List<DamageEvent> damageEvents = new List<DamageEvent>();
    
    [Header("Hit Detection")]
    public float velocityThreshold = {config['hit_detection']['velocity_threshold']}f;
    public float detectionRadius = {config['hit_detection']['detection_radius']}f;
    public LayerMask targetLayers;
    
    [Header("Combo Settings")]
    public int comboFrame = {config['combo_data']['can_combo_after_frame']};
    public float comboWindow = {config['combo_data']['combo_window']}f;
    
    [Header("Cancel Settings")]
    public int cancelFrame = {config['cancel_data']['can_cancel_after_frame']};
    public int cancelCost = {config['cancel_data']['cancel_cost']};
    
    void OnEnable()
    {{
        InitializeDamageEvents();
    }}
    
    void InitializeDamageEvents()
    {{
        damageEvents.Clear();
        
        // Primary damage event
        damageEvents.Add(new DamageEvent
        {{
            frame = {config['damage_events'][0]['frame']},
            time = {config['damage_events'][0]['time']}f,
            boneName = "{config['damage_events'][0]['bone']}",
            damageMultiplier = {config['damage_events'][0]['damage_multiplier']}f,
            knockbackForce = {config['damage_events'][0]['knockback_force']},
            soundEffect = "{config['damage_events'][0]['sound_effect']}",
            particleEffect = "{config['damage_events'][0]['particle_effect']}"
        }});
        
        // Secondary damage event
        damageEvents.Add(new DamageEvent
        {{
            frame = {config['damage_events'][1]['frame']},
            time = {config['damage_events'][1]['time']}f,
            boneName = "{config['damage_events'][1]['bone']}",
            damageMultiplier = {config['damage_events'][1]['damage_multiplier']}f,
            knockbackForce = {config['damage_events'][1]['knockback_force']},
            soundEffect = "{config['damage_events'][1]['sound_effect']}",
            particleEffect = "{config['damage_events'][1]['particle_effect']}"
        }});
    }}
}}

[System.Serializable]
public class DamageEvent
{{
    public int frame;
    public float time;
    public string boneName;
    public float damageMultiplier = 1.0f;
    public float knockbackForce = 500f;
    public string soundEffect;
    public string particleEffect;
    public Vector3 hitBoxSize = Vector3.one;
}}"""
    
    return unity_content

def main():
    if len(sys.argv) != 2:
        print("Sử dụng: python generate_damage_config.py <file.ani>")
        return
    
    ani_file = sys.argv[1]
    if not os.path.exists(ani_file):
        print(f"✗ File không tồn tại: {ani_file}")
        return
    
    print("🔧 GENERATING DAMAGE CONFIG FILES")
    print("="*40)
    
    # Tạo config
    config = create_damage_config(ani_file)
    
    # Export JSON config
    json_file = ani_file.replace('.ani', '_damage_config.json')
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    print(f"✓ JSON Config: {json_file}")
    
    # Export C++ header
    cpp_content = create_cpp_header(config)
    cpp_file = ani_file.replace('.ani', '_DamageConfig.h')
    with open(cpp_file, 'w', encoding='utf-8') as f:
        f.write(cpp_content)
    print(f"✓ C++ Header: {cpp_file}")
    
    # Export Unity script
    unity_content = create_unity_script(config)
    unity_file = ani_file.replace('.ani', '_Config.cs')
    with open(unity_file, 'w', encoding='utf-8') as f:
        f.write(unity_content)
    print(f"✓ Unity Script: {unity_file}")
    
    print("\\n🎯 IMPLEMENTATION GUIDE:")
    print("1. Import JSON config vào game engine")
    print("2. Sử dụng C++ header cho Unreal/Custom engine")
    print("3. Sử dụng Unity script cho Unity projects")
    print("4. Monitor bone velocity để trigger damage")
    print(f"5. Primary damage tại frame {config['damage_events'][0]['frame']} ({config['damage_events'][0]['time']:.3f}s)")

if __name__ == "__main__":
    main()
